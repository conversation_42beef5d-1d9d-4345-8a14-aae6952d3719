import { computed, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAdminStore } from '@/stores/admin';

export interface NavigationItem {
  key: string;
  label: string;
  icon: string;
  route?: string;
  hasChildren?: boolean;
  expanded?: boolean;
  children?: Array<{
    key: string;
    label: string;
    route: string;
    badge?: string | number;
  }>;
  active?: boolean;
  badge?: string | number;
  permission?: string;
}

export function useAdminNavigation() {
  const adminStore = useAdminStore();
  const route = useRoute();
  const router = useRouter();

  // Reactive state
  const searchQuery = ref('');
  const filteredItems = ref<NavigationItem[]>([]);

  // Navigation items configuration
  const navigationItems = computed((): NavigationItem[] => [
    {
      key: 'dashboard',
      label: 'Dashboard',
      icon: 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z',
      route: '/admin-spa/dashboard',
      active: route.path === '/admin-spa/dashboard' || route.path === '/admin-spa'
    },
    {
      key: 'sales',
      label: 'Sales',
      icon: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
      hasChildren: true,
      expanded: adminStore.openMenus.sales,
      children: [
        { key: 'sales-overview', label: 'Sales Overview', route: '/admin-spa/sales' },
        { key: 'sales-orders', label: 'Orders', route: '/admin-spa/sales/orders' },
        { key: 'sales-invoices', label: 'Invoices', route: '/admin-spa/sales/invoices' },
        { key: 'sales-customers', label: 'Customers', route: '/admin-spa/sales/customers' },
        { key: 'sales-reports', label: 'Sales Reports', route: '/admin-spa/sales/reports' }
      ]
    },
    {
      key: 'auctions',
      label: 'Auctions',
      icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',
      hasChildren: true,
      expanded: adminStore.openMenus.auctions,
      children: [
        { key: 'auctions-all', label: 'All Auctions', route: '/admin-spa/auctions' },
        { key: 'auctions-create', label: 'Create Auction', route: '/admin-spa/auctions/create' },
        { key: 'auctions-live', label: 'Live Auctions', route: '/admin-spa/auctions/live', badge: '3' },
        { key: 'auctions-ended', label: 'Ended Auctions', route: '/admin-spa/auctions/ended' },
        { key: 'auctions-templates', label: 'Auction Templates', route: '/admin-spa/auctions/templates' }
      ]
    },
    {
      key: 'items',
      label: 'Items',
      icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
      hasChildren: true,
      expanded: adminStore.openMenus.items,
      children: [
        { key: 'items-all', label: 'All Items', route: '/admin-spa/items' },
        { key: 'items-add', label: 'Add Item', route: '/admin-spa/items/create' },
        { key: 'items-categories', label: 'Categories', route: '/admin-spa/items/categories' },
        { key: 'items-bulk', label: 'Bulk Import', route: '/admin-spa/items/bulk-import' },
        { key: 'items-conditions', label: 'Item Conditions', route: '/admin-spa/items/conditions' }
      ]
    },
    {
      key: 'auction-types',
      label: 'Auction Types',
      icon: 'M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z',
      hasChildren: true,
      expanded: adminStore.openMenus.auctionTypes,
      children: [
        { key: 'auction-types-all', label: 'All Types', route: '/admin-spa/auction-types' },
        { key: 'auction-types-create', label: 'Create Type', route: '/admin-spa/auction-types/create' },
        { key: 'auction-types-online', label: 'Online Auctions', route: '/admin-spa/auction-types/list?type=online' },
        { key: 'auction-types-live', label: 'Live Auctions', route: '/admin-spa/auction-types/list?type=live' },
        { key: 'auction-types-cash', label: 'Cash Sales', route: '/admin-spa/auction-types/list?type=cash' }
      ]
    },
    {
      key: 'auction-listings',
      label: 'Auction Listings',
      icon: 'M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2zm0 0V3a2 2 0 012-2h2a2 2 0 012 2v2m-6 0h6m0 0v6a2 2 0 01-2 2h-2a2 2 0 01-2-2V7',
      hasChildren: true,
      expanded: adminStore.openMenus.auctionListings,
      children: [
        { key: 'auction-listings-all', label: 'All Listings', route: '/admin-spa/auction-listings' },
        { key: 'auction-listings-create', label: 'Create Listing', route: '/admin-spa/auction-listings/create' },
        { key: 'auction-listings-active', label: 'Active Auctions', route: '/admin-spa/auction-listings/list?status=active' },
        { key: 'auction-listings-closed', label: 'Closed Auctions', route: '/admin-spa/auction-listings/list?status=closed' }
      ]
    },
    {
      key: 'users',
      label: 'Users',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
      hasChildren: true,
      expanded: adminStore.openMenus.users,
      children: [
        { key: 'users-all', label: 'All Users', route: '/admin-spa/users' },
        { key: 'users-bidders', label: 'Bidders', route: '/admin-spa/users/bidders' },
        { key: 'users-sellers', label: 'Sellers', route: '/admin-spa/users/sellers' },
        { key: 'users-admins', label: 'Administrators', route: '/admin-spa/users/administrators' },
        { key: 'users-roles', label: 'User Roles', route: '/admin-spa/users/roles' },
        { key: 'users-permissions', label: 'Permissions', route: '/admin-spa/users/permissions' }
      ]
    },
    {
      key: 'financial',
      label: 'Financial',
      icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
      hasChildren: true,
      expanded: adminStore.openMenus.financial,
      children: [
        { key: 'financial-transactions', label: 'Transactions', route: '/admin-spa/financial/transactions' },
        { key: 'financial-payments', label: 'Payments', route: '/admin-spa/financial/payments', badge: '12' },
        { key: 'financial-commissions', label: 'Commissions', route: '/admin-spa/financial/commissions' },
        { key: 'financial-invoices', label: 'Invoices', route: '/admin-spa/financial/invoices' },
        { key: 'financial-tax', label: 'Tax Reports', route: '/admin-spa/financial/tax-reports' }
      ]
    },
    {
      key: 'reports',
      label: 'Reports',
      icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
      hasChildren: true,
      expanded: adminStore.openMenus.reports,
      children: [
        { key: 'reports-sales', label: 'Sales Reports', route: '/admin-spa/reports/sales' },
        { key: 'reports-analytics', label: 'User Analytics', route: '/admin-spa/reports/analytics' },
        { key: 'reports-performance', label: 'Performance', route: '/admin-spa/reports/performance' },
        { key: 'reports-custom', label: 'Custom Reports', route: '/admin-spa/reports/custom' }
      ]
    },
    {
      key: 'settings',
      label: 'Settings',
      icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
      hasChildren: true,
      expanded: adminStore.openMenus.settings,
      children: [
        { key: 'settings-general', label: 'General', route: '/admin-spa/settings/general' },
        { key: 'settings-auctions', label: 'Auction Settings', route: '/admin-spa/settings/auctions' },
        { key: 'settings-payments', label: 'Payment Gateway', route: '/admin-spa/settings/payments' },
        { key: 'settings-email', label: 'Email Templates', route: '/admin-spa/settings/email' },
        { key: 'settings-logs', label: 'System Logs', route: '/admin-spa/settings/logs' }
      ]
    }
  ]);

  // Computed properties
  const currentItem = computed(() => {
    return findItemByRoute(route.path);
  });

  const breadcrumbs = computed(() => {
    const item = currentItem.value;
    if (!item) return [];

    const crumbs = [];
    
    // Add parent if this is a child item
    if (item.parent) {
      crumbs.push({
        label: item.parent.label,
        route: item.parent.route
      });
    }
    
    // Add current item
    crumbs.push({
      label: item.label,
      route: item.route,
      current: true
    });

    return crumbs;
  });

  // Methods
  const findItemByRoute = (routePath: string) => {
    for (const item of navigationItems.value) {
      if (item.route === routePath) {
        return item;
      }
      
      if (item.children) {
        for (const child of item.children) {
          if (child.route === routePath) {
            return {
              ...child,
              parent: item
            };
          }
        }
      }
    }
    return null;
  };

  const navigateTo = (routePath: string) => {
    router.push(routePath);
  };

  const toggleMenu = (menuKey: string) => {
    adminStore.toggleMenu(menuKey);
  };

  const searchNavigation = (query: string) => {
    searchQuery.value = query;
    
    if (!query.trim()) {
      filteredItems.value = [];
      return;
    }

    const results: NavigationItem[] = [];
    const lowerQuery = query.toLowerCase();

    navigationItems.value.forEach(item => {
      // Check main item
      if (item.label.toLowerCase().includes(lowerQuery)) {
        results.push(item);
      }

      // Check children
      if (item.children) {
        item.children.forEach(child => {
          if (child.label.toLowerCase().includes(lowerQuery)) {
            results.push({
              ...child,
              parent: item
            } as NavigationItem);
          }
        });
      }
    });

    filteredItems.value = results;
  };

  const clearSearch = () => {
    searchQuery.value = '';
    filteredItems.value = [];
  };

  const isActiveRoute = (routePath: string) => {
    return route.path === routePath;
  };

  const isParentActive = (item: NavigationItem) => {
    if (!item.children) return false;
    return item.children.some(child => route.path === child.route);
  };

  return {
    // State
    navigationItems,
    currentItem,
    breadcrumbs,
    searchQuery,
    filteredItems,

    // Methods
    findItemByRoute,
    navigateTo,
    toggleMenu,
    searchNavigation,
    clearSearch,
    isActiveRoute,
    isParentActive
  };
}
